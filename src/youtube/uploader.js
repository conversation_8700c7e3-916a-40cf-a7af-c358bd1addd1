import BrowserManager from '../utils/browser.js';
import config from '../config/config.js';
import chalk from 'chalk';
import path from 'path';

export class YouTubeUploader {
  constructor() {
    this.browser = new BrowserManager();
    this.isLoggedIn = false;
  }

  async initialize() {
    try {
      await this.browser.launch();
      console.log(chalk.green('✅ YouTube uploader initialized'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize YouTube uploader:'), error);
      throw error;
    }
  }

  async login() {
    try {
      if (this.isLoggedIn) {
        console.log(chalk.yellow('⚠️ Already logged in to YouTube'));
        return true;
      }

      console.log(chalk.blue('🔐 Logging into YouTube...'));
      
      await this.browser.navigateTo('https://accounts.google.com/signin');
      
      // Enter email
      await this.browser.safeType('input[type="email"]', config.youtube.email);
      await this.browser.safeClick('#identifierNext');
      
      // Wait for password field
      await this.browser.waitForSelector('input[type="password"]', { timeout: 10000 });
      
      // Enter password
      await this.browser.safeType('input[type="password"]', config.youtube.password);
      await this.browser.safeClick('#passwordNext');
      
      // Wait for login to complete
      await this.browser.waitForNavigation();
      
      // Navigate to YouTube Studio
      await this.browser.navigateTo(config.youtube.studioUrl);
      
      // Check if we're successfully logged in
      const isStudioLoaded = await this.browser.isElementVisible('[aria-label="Create"]');
      
      if (isStudioLoaded) {
        this.isLoggedIn = true;
        console.log(chalk.green('✅ Successfully logged into YouTube'));
        return true;
      } else {
        throw new Error('Failed to access YouTube Studio');
      }
    } catch (error) {
      console.error(chalk.red('❌ YouTube login failed:'), error);
      throw error;
    }
  }

  async uploadVideo(videoPath, metadata = {}) {
    try {
      if (!this.isLoggedIn) {
        await this.login();
      }

      console.log(chalk.blue(`📤 Uploading video: ${path.basename(videoPath)}`));
      
      // Navigate to upload page
      await this.browser.navigateTo(`${config.youtube.studioUrl}/channel/UC/videos/upload`);

      // Try multiple selectors for the create/upload button
      try {
        // Try the create button first
        await this.browser.safeClick('[aria-label="Create"]');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Try different upload video selectors
        const uploadSelectors = [
          'text="Upload video"',
          'text="Upload videos"',
          '[aria-label="Upload video"]',
          '[aria-label="Upload videos"]',
          'text="Upload"',
          '[data-testid="upload-button"]',
          'button:has-text("Upload")',
          'a:has-text("Upload")'
        ];

        let uploadClicked = false;
        for (const selector of uploadSelectors) {
          try {
            await this.browser.safeClick(selector);
            uploadClicked = true;
            break;
          } catch (error) {
            console.log(chalk.yellow(`⚠️ Selector ${selector} not found, trying next...`));
          }
        }

        if (!uploadClicked) {
          throw new Error('Could not find upload button');
        }
      } catch (error) {
        // If create button doesn't work, try direct navigation to upload
        console.log(chalk.yellow('⚠️ Trying direct upload URL...'));
        await this.browser.navigateTo(`${config.youtube.studioUrl}/channel/UC/videos/upload?d=ud`);
      }
      
      // Upload file - handle hidden file input
      try {
        // Wait for the page to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Try to find and use the file input (even if hidden)
        const fileInput = await this.browser.page.locator('input[type="file"]').first();
        await fileInput.setInputFiles(videoPath);
        console.log(chalk.green('✅ File selected successfully'));
      } catch (error) {
        console.log(chalk.yellow('⚠️ Direct file input failed, trying alternative method...'));

        // Alternative: try to click the upload area to trigger file dialog
        const uploadSelectors = [
          '[role="button"]:has-text("SELECT FILES")',
          'button:has-text("SELECT FILES")',
          '[aria-label="Select files"]',
          '.upload-button',
          '[data-testid="select-files-button"]',
          'text="SELECT FILES"'
        ];

        let uploadTriggered = false;
        for (const selector of uploadSelectors) {
          try {
            await this.browser.safeClick(selector);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Now try the file input again
            const fileInput = await this.browser.page.locator('input[type="file"]').first();
            await fileInput.setInputFiles(videoPath);
            uploadTriggered = true;
            console.log(chalk.green('✅ File selected via alternative method'));
            break;
          } catch (altError) {
            console.log(chalk.yellow(`⚠️ Alternative selector ${selector} failed`));
          }
        }

        if (!uploadTriggered) {
          throw new Error('Could not upload file using any method');
        }
      }
      
      console.log(chalk.yellow('⏳ File uploading...'));
      
      // Wait for upload to process - try multiple indicators
      const processingSelectors = [
        'text="Processing"',
        'text="Processing..."',
        'text="Uploading"',
        'text="Uploading..."',
        '[aria-label*="Processing"]',
        '[aria-label*="Uploading"]',
        'text="Upload complete"',
        'text="Upload completed"',
        '[data-testid="processing"]',
        '.upload-progress',
        'text="Details"' // Sometimes it goes straight to details
      ];

      let processingDetected = false;
      for (const selector of processingSelectors) {
        try {
          await this.browser.waitForSelector(selector, { timeout: 10000 });
          console.log(chalk.green(`✅ Upload progress detected: ${selector}`));
          processingDetected = true;
          break;
        } catch (error) {
          console.log(chalk.yellow(`⚠️ Selector ${selector} not found, trying next...`));
        }
      }

      if (!processingDetected) {
        console.log(chalk.yellow('⚠️ No processing indicator found, continuing anyway...'));
      }

      // Wait a bit for upload to complete
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Fill in video details
      await this.fillVideoDetails(metadata);
      
      // Set visibility and publish
      await this.setVisibilityAndPublish(metadata.visibility || 'public');
      
      console.log(chalk.green('✅ Video uploaded successfully'));
      
      return {
        success: true,
        videoPath,
        metadata
      };
    } catch (error) {
      console.error(chalk.red('❌ Video upload failed:'), error);
      return {
        success: false,
        error: error.message,
        videoPath
      };
    }
  }

  async fillVideoDetails(metadata) {
    try {
      // Wait for the details page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Title - try multiple selectors
      if (metadata.title) {
        const titleSelectors = [
          '[aria-label="Add a title that describes your video"]',
          '[aria-label*="title"]',
          '[placeholder*="title"]',
          '[placeholder*="Title"]',
          'input[aria-label*="title"]',
          'textarea[aria-label*="title"]',
          '#textbox[aria-label*="title"]',
          '[data-testid="title-input"]',
          'input[type="text"]',
          'textarea'
        ];

        let titleFilled = false;
        for (const selector of titleSelectors) {
          try {
            await this.browser.safeType(selector, metadata.title);
            console.log(chalk.green(`✅ Title filled using: ${selector}`));
            titleFilled = true;
            break;
          } catch (error) {
            console.log(chalk.yellow(`⚠️ Title selector ${selector} failed, trying next...`));
          }
        }

        if (!titleFilled) {
          console.log(chalk.yellow('⚠️ Could not fill title, continuing...'));
        }
      }
      
      // Description - try multiple selectors
      if (metadata.description) {
        const descriptionSelectors = [
          '[aria-label="Tell viewers about your video"]',
          '[aria-label*="description"]',
          '[placeholder*="description"]',
          '[placeholder*="Description"]',
          'textarea[aria-label*="description"]',
          '[data-testid="description-input"]',
          'textarea'
        ];

        let descriptionFilled = false;
        for (const selector of descriptionSelectors) {
          try {
            await this.browser.safeType(selector, metadata.description);
            console.log(chalk.green(`✅ Description filled using: ${selector}`));
            descriptionFilled = true;
            break;
          } catch (error) {
            console.log(chalk.yellow(`⚠️ Description selector ${selector} failed, trying next...`));
          }
        }

        if (!descriptionFilled) {
          console.log(chalk.yellow('⚠️ Could not fill description, continuing...'));
        }
      }
      
      // Thumbnail (if provided)
      if (metadata.thumbnail) {
        const thumbnailInput = await this.browser.page.$('input[accept="image/jpeg,image/jpg,image/png,image/gif,image/bmp"]');
        if (thumbnailInput) {
          await thumbnailInput.setInputFiles(metadata.thumbnail);
        }
      }
      
      // Tags
      if (metadata.tags && metadata.tags.length > 0) {
        // Click on "More options"
        await this.browser.safeClick('text="More options"');
        
        // Add tags
        const tagsInput = await this.browser.waitForSelector('[aria-label="Tags"]');
        await tagsInput.type(metadata.tags.join(', '));
      }
      
      console.log(chalk.green('✅ Video details filled'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to fill video details:'), error);
      throw error;
    }
  }

  async setVisibilityAndPublish(visibility = 'public') {
    try {
      // Click Next to go to visibility settings - try multiple times
      const nextSelectors = [
        'text="Next"',
        'button:has-text("Next")',
        '[aria-label="Next"]',
        '[data-testid="next-button"]',
        'button[aria-label*="Next"]'
      ];

      // Try to click Next 3 times to get to visibility settings
      for (let i = 0; i < 3; i++) {
        let nextClicked = false;
        for (const selector of nextSelectors) {
          try {
            await this.browser.safeClick(selector);
            console.log(chalk.green(`✅ Next clicked (${i + 1}/3) using: ${selector}`));
            nextClicked = true;
            await new Promise(resolve => setTimeout(resolve, 2000));
            break;
          } catch (error) {
            console.log(chalk.yellow(`⚠️ Next selector ${selector} failed, trying next...`));
          }
        }

        if (!nextClicked) {
          console.log(chalk.yellow(`⚠️ Could not click Next (${i + 1}/3), continuing...`));
        }
      }
      
      // Set visibility
      const visibilityMap = {
        'public': 'PUBLIC',
        'unlisted': 'UNLISTED',
        'private': 'PRIVATE'
      };
      
      const visibilityOption = visibilityMap[visibility.toLowerCase()] || 'PUBLIC';
      await this.browser.safeClick(`[name="${visibilityOption}"]`);
      
      // Publish
      await this.browser.safeClick('text="Publish"');
      
      // Wait for confirmation
      await this.browser.waitForSelector('text="Video published"', { timeout: 30000 });
      
      console.log(chalk.green(`✅ Video published as ${visibility}`));
    } catch (error) {
      console.error(chalk.red('❌ Failed to publish video:'), error);
      throw error;
    }
  }

  async uploadShorts(videoPath, metadata = {}) {
    try {
      // Add #Shorts to title and description for YouTube Shorts
      const shortsMetadata = {
        ...metadata,
        title: metadata.title ? `${metadata.title} #Shorts` : '#Shorts',
        description: metadata.description ? `${metadata.description}\n\n#Shorts` : '#Shorts',
        tags: [...(metadata.tags || []), 'Shorts', 'YouTube Shorts']
      };
      
      return await this.uploadVideo(videoPath, shortsMetadata);
    } catch (error) {
      console.error(chalk.red('❌ Shorts upload failed:'), error);
      throw error;
    }
  }

  async uploadMultipleVideos(videoPaths, metadataArray = []) {
    const results = [];
    
    for (let i = 0; i < videoPaths.length; i++) {
      const videoPath = videoPaths[i];
      const metadata = metadataArray[i] || {};
      
      try {
        const result = await this.uploadVideo(videoPath, metadata);
        results.push(result);
        
        // Rate limiting between uploads
        if (i < videoPaths.length - 1) {
          console.log(chalk.yellow(`⏳ Waiting ${config.delays.upload / 1000}s before next upload...`));
          await new Promise(resolve => setTimeout(resolve, config.delays.upload));
        }
      } catch (error) {
        console.error(chalk.red(`❌ Failed to upload ${videoPath}:`), error);
        results.push({
          success: false,
          error: error.message,
          videoPath
        });
      }
    }
    
    return results;
  }

  async getChannelInfo() {
    try {
      if (!this.isLoggedIn) {
        await this.login();
      }
      
      await this.browser.navigateTo(`${config.youtube.studioUrl}/channel`);
      
      // Extract channel information
      const channelName = await this.browser.page.textContent('[data-testid="channel-name"]').catch(() => 'Unknown');
      const subscriberCount = await this.browser.page.textContent('[data-testid="subscriber-count"]').catch(() => '0');
      
      return {
        name: channelName,
        subscribers: subscriberCount
      };
    } catch (error) {
      console.error(chalk.red('❌ Failed to get channel info:'), error);
      return null;
    }
  }

  async close() {
    try {
      await this.browser.close();
      console.log(chalk.green('✅ YouTube uploader closed'));
    } catch (error) {
      console.error(chalk.red('❌ Error closing YouTube uploader:'), error);
    }
  }
}

export default YouTubeUploader;
