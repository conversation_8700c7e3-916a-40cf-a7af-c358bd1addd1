import ffmpegPkg from 'fluent-ffmpeg';
const ffmpeg = ffmpegPkg;
import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import config from '../config/config.js';

export class VideoProcessor {
  constructor() {
    this.outputDir = config.paths.output;
    this.tempDir = config.paths.temp;
  }

  async initialize() {
    try {
      await fs.ensureDir(this.outputDir);
      await fs.ensureDir(this.tempDir);
      console.log(chalk.green('✅ Video processor initialized'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize video processor:'), error);
      throw error;
    }
  }

  async convertToShorts(inputPath, options = {}) {
    try {
      console.log(chalk.blue(`🎬 Converting to shorts format: ${path.basename(inputPath)}`));

      const outputPath = this.generateOutputPath(inputPath, 'shorts', options);
      const format = options.platform === 'instagram' ? 'instagram' : 'youtube';
      const videoConfig = config.video.formats[format];
      const startTime = options.startTime || 0;

      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .seekInput(startTime)
          .duration(config.video.maxDuration)
          .size(`${videoConfig.width}x${videoConfig.height}`)
          .fps(videoConfig.fps)
          .videoCodec('libopenh264')
          .audioCodec('aac')
          .audioBitrate('192k')  // Increased audio bitrate
          .audioFrequency(44100)
          .audioChannels(2)
          .videoBitrate('2500k')  // Increased video bitrate
          .autopad(true, 'black')
          .outputOptions([
            '-movflags', '+faststart',  // Better streaming
            '-preset', 'medium',        // Better quality
            '-crf', '23'               // Better quality
          ])
          .output(outputPath)
          .on('start', () => {
            console.log(chalk.blue('🔄 FFmpeg process started'));
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              console.log(chalk.yellow(`⏳ Progress: ${Math.round(progress.percent)}%`));
            }
          })
          .on('end', () => {
            console.log(chalk.green(`✅ Conversion completed: ${outputPath}`));
            resolve({
              success: true,
              outputPath,
              format: format
            });
          })
          .on('error', (error) => {
            console.error(chalk.red('❌ FFmpeg error:'), error);
            reject(error);
          })
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Video conversion failed:'), error);
      throw error;
    }
  }

  async extractHighlights(inputPath, segments = 3) {
    try {
      console.log(chalk.blue(`✂️ Extracting highlights from: ${path.basename(inputPath)}`));
      
      const duration = await this.getVideoDuration(inputPath);
      const segmentDuration = Math.min(config.video.maxDuration / segments, duration / segments);
      const highlights = [];
      
      for (let i = 0; i < segments; i++) {
        const startTime = (duration / segments) * i;
        const outputPath = this.generateOutputPath(inputPath, `highlight-${i + 1}`);
        
        await this.extractSegment(inputPath, outputPath, startTime, segmentDuration);
        highlights.push(outputPath);
      }
      
      console.log(chalk.green(`✅ Extracted ${highlights.length} highlights`));
      return highlights;
    } catch (error) {
      console.error(chalk.red('❌ Highlight extraction failed:'), error);
      throw error;
    }
  }

  async extractSegment(inputPath, outputPath, startTime, duration) {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .seekInput(startTime)
        .duration(duration)
        .output(outputPath)
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .run();
    });
  }

  async addCaptions(inputPath, captionText, options = {}) {
    try {
      console.log(chalk.blue(`📝 Adding captions to: ${path.basename(inputPath)}`));

      const outputPath = this.generateOutputPath(inputPath, 'captioned', options);
      const fontSize = options.fontSize || 24;
      const fontColor = options.fontColor || 'white';
      const backgroundColor = options.backgroundColor || 'black@0.5';
      
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .videoFilters([
            {
              filter: 'drawtext',
              options: {
                text: captionText,
                fontsize: fontSize,
                fontcolor: fontColor,
                box: 1,
                boxcolor: backgroundColor,
                boxborderw: 5,
                x: '(w-text_w)/2',
                y: 'h-th-20'
              }
            }
          ])
          .output(outputPath)
          .on('end', () => {
            console.log(chalk.green(`✅ Captions added: ${outputPath}`));
            resolve(outputPath);
          })
          .on('error', reject)
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Caption addition failed:'), error);
      throw error;
    }
  }

  async addWatermark(inputPath, watermarkPath, options = {}) {
    try {
      console.log(chalk.blue(`🏷️ Adding watermark to: ${path.basename(inputPath)}`));
      
      const outputPath = this.generateOutputPath(inputPath, 'watermarked');
      const position = options.position || 'bottom-right';
      
      const positionMap = {
        'top-left': '10:10',
        'top-right': 'W-w-10:10',
        'bottom-left': '10:H-h-10',
        'bottom-right': 'W-w-10:H-h-10',
        'center': '(W-w)/2:(H-h)/2'
      };
      
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .input(watermarkPath)
          .complexFilter([
            `[1:v]scale=100:100[watermark]`,
            `[0:v][watermark]overlay=${positionMap[position]}[output]`
          ])
          .map('[output]')
          .output(outputPath)
          .on('end', () => {
            console.log(chalk.green(`✅ Watermark added: ${outputPath}`));
            resolve(outputPath);
          })
          .on('error', reject)
          .run();
      });
    } catch (error) {
      console.error(chalk.red('❌ Watermark addition failed:'), error);
      throw error;
    }
  }

  async getVideoDuration(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          resolve(metadata.format.duration);
        }
      });
    });
  }

  async getVideoInfo(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          const { format, streams } = metadata;
          const videoStream = streams.find(s => s.codec_type === 'video');
          
          resolve({
            duration: format.duration,
            size: format.size,
            bitrate: format.bit_rate,
            width: videoStream?.width,
            height: videoStream?.height,
            fps: eval(videoStream?.r_frame_rate) || 30
          });
        }
      });
    });
  }

  generateOutputPath(inputPath, suffix, options = {}) {
    const ext = path.extname(inputPath);
    const basename = path.basename(inputPath, ext);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Include start time and clip index in filename to avoid duplicates
    let filename = basename;

    if (options.startTime !== undefined) {
      filename += `-${Math.round(options.startTime)}s`;
    }

    if (options.clipIndex !== undefined) {
      filename += `-clip${options.clipIndex}`;
    }

    filename += `-${suffix}-${timestamp}${ext}`;

    return path.join(this.outputDir, filename);
  }

  async cleanup() {
    try {
      // Clean up temp files
      const tempFiles = await fs.readdir(this.tempDir);
      for (const file of tempFiles) {
        await fs.remove(path.join(this.tempDir, file));
      }
      console.log(chalk.green('✅ Temp files cleaned up'));
    } catch (error) {
      console.error(chalk.red('❌ Cleanup failed:'), error);
    }
  }

  async removeDuplicateFiles() {
    try {
      console.log(chalk.blue('🧹 Checking for duplicate files...'));

      const outputDir = this.outputDir;
      const files = await fs.readdir(outputDir);

      // Group files by video ID and start time
      const fileGroups = {};

      for (const file of files) {
        if (!file.endsWith('.mp4')) continue;

        // Extract video ID and start time from filename
        const match = file.match(/^(.+?)-(\d+)s-/);
        if (match) {
          const [, videoId, startTime] = match;
          const key = `${videoId}-${startTime}s`;

          if (!fileGroups[key]) {
            fileGroups[key] = [];
          }
          fileGroups[key].push(file);
        }
      }

      // Remove duplicates (keep the most recent one)
      let removedCount = 0;
      for (const [key, duplicates] of Object.entries(fileGroups)) {
        if (duplicates.length > 1) {
          // Sort by timestamp (newest first)
          duplicates.sort((a, b) => {
            const timestampA = a.match(/-(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)\.mp4$/)?.[1];
            const timestampB = b.match(/-(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)\.mp4$/)?.[1];
            return timestampB?.localeCompare(timestampA) || 0;
          });

          // Remove all but the newest
          for (let i = 1; i < duplicates.length; i++) {
            const filePath = path.join(outputDir, duplicates[i]);
            await fs.remove(filePath);
            console.log(chalk.yellow(`🗑️ Removed duplicate: ${duplicates[i]}`));
            removedCount++;
          }
        }
      }

      if (removedCount > 0) {
        console.log(chalk.green(`✅ Removed ${removedCount} duplicate files`));
      } else {
        console.log(chalk.green('✅ No duplicate files found'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Failed to remove duplicates:'), error);
    }
  }

  async createMultipleShorts(inputPath, platform = 'youtube', numShorts = null) {
    try {
      console.log(chalk.blue(`🎬 Creating multiple 1-minute shorts from: ${path.basename(inputPath)}`));

      // Get video duration first
      const duration = await this.getVideoDuration(inputPath);
      const shortDuration = 60; // Always 1 minute

      // Calculate how many 1-minute clips we can make
      const maxPossibleShorts = Math.floor(duration / shortDuration);
      const actualNumShorts = numShorts ? Math.min(numShorts, maxPossibleShorts) : Math.min(maxPossibleShorts, 10); // Max 10 clips

      if (duration < shortDuration) {
        console.log(chalk.yellow(`⚠️ Video too short (${Math.round(duration)}s), creating single short`));
        return [await this.processVideoForPlatform(inputPath, platform)];
      }

      console.log(chalk.blue(`📊 Video duration: ${Math.round(duration)}s, creating ${actualNumShorts} shorts`));

      const results = [];

      // Create clips with better spacing - avoid overlaps and get best parts
      for (let i = 0; i < actualNumShorts; i++) {
        // Distribute clips evenly across the video duration
        const startTime = Math.floor((duration / (actualNumShorts + 1)) * (i + 1));

        // Ensure we don't go past the video end
        const adjustedStartTime = Math.min(startTime, duration - shortDuration);

        console.log(chalk.blue(`📹 Creating short ${i + 1}/${actualNumShorts} starting at ${Math.round(adjustedStartTime)}s`));

        try {
          // Check if this clip already exists to avoid duplicates
          const clipExists = await this.checkClipExists(inputPath, adjustedStartTime, platform);
          if (clipExists) {
            console.log(chalk.yellow(`⚠️ Clip ${i + 1} already exists, skipping`));
            continue;
          }

          const result = await this.processVideoForPlatform(inputPath, platform, {
            startTime: adjustedStartTime,
            clipIndex: i + 1
          });
          results.push(result);
        } catch (error) {
          console.error(chalk.red(`❌ Failed to create short ${i + 1}:`), error);
        }
      }

      console.log(chalk.green(`✅ Created ${results.length} shorts from ${path.basename(inputPath)}`));
      return results;
    } catch (error) {
      console.error(chalk.red('❌ Failed to create multiple shorts:'), error);
      throw error;
    }
  }

  async checkClipExists(inputPath, startTime, platform) {
    try {
      const videoId = path.basename(inputPath, path.extname(inputPath));
      const outputDir = config.video.outputDir;
      const files = await fs.readdir(outputDir);

      // Check if a clip starting at this time already exists
      const existingClip = files.find(file =>
        file.includes(videoId) &&
        file.includes(`-${Math.round(startTime)}s-`) &&
        file.includes('shorts')
      );

      return !!existingClip;
    } catch (error) {
      return false; // If we can't check, assume it doesn't exist
    }
  }

  async getVideoDuration(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          resolve(metadata.format.duration);
        }
      });
    });
  }

  async processVideoForPlatform(inputPath, platform = 'youtube', options = {}) {
    try {
      console.log(chalk.blue(`🎯 Processing video for ${platform}`));

      // Convert to shorts format
      const shortsResult = await this.convertToShorts(inputPath, { platform, ...options });

      // Add captions with the same options to maintain naming consistency
      const captionedResult = await this.addCaptions(shortsResult.outputPath, 'Auto-generated captions', options);

      return {
        success: true,
        originalPath: inputPath,
        shortsPath: shortsResult.outputPath,
        finalPath: captionedResult.outputPath,
        platform: platform,
        startTime: options.startTime,
        clipIndex: options.clipIndex
      };
    } catch (error) {
      console.error(chalk.red(`❌ Failed to process video for ${platform}:`), error);
      throw error;
    }
  }
}

export default VideoProcessor;
