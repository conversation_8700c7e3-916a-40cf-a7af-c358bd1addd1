import { chromium } from 'playwright';
import config from '../config/config.js';
import chalk from 'chalk';

export class BrowserManager {
  constructor() {
    this.browser = null;
    this.context = null;
    this.page = null;
  }

  async launch() {
    try {
      console.log(chalk.blue('🚀 Launching browser...'));
      
      this.browser = await chromium.launch({
        headless: config.browser.headless,
        slowMo: config.browser.slowMo,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      this.context = await this.browser.newContext({
        userAgent: config.browser.userAgent,
        viewport: { width: 1920, height: 1080 },
        locale: 'en-US',
        timezoneId: 'America/New_York'
      });

      this.page = await this.context.newPage();
      
      // Set default timeout
      this.page.setDefaultTimeout(config.browser.timeout);
      
      console.log(chalk.green('✅ Browser launched successfully'));
      return this.page;
    } catch (error) {
      console.error(chalk.red('❌ Failed to launch browser:'), error);
      throw error;
    }
  }

  async close() {
    try {
      if (this.browser) {
        await this.browser.close();
        console.log(chalk.green('✅ Browser closed'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Error closing browser:'), error);
    }
  }

  async waitForSelector(selector, options = {}) {
    try {
      return await this.page.waitForSelector(selector, {
        timeout: config.browser.timeout,
        ...options
      });
    } catch (error) {
      console.error(chalk.red(`❌ Element not found: ${selector}`));
      throw error;
    }
  }

  async safeClick(selector, options = {}) {
    try {
      await this.waitForSelector(selector);
      await this.page.click(selector, options);
      await this.page.waitForTimeout(config.delays.action);
    } catch (error) {
      console.error(chalk.red(`❌ Failed to click: ${selector}`));
      throw error;
    }
  }

  async safeType(selector, text, options = {}) {
    try {
      await this.waitForSelector(selector);
      await this.page.fill(selector, '');
      await this.page.type(selector, text, {
        delay: config.delays.typing,
        ...options
      });
      await this.page.waitForTimeout(config.delays.action);
    } catch (error) {
      console.error(chalk.red(`❌ Failed to type in: ${selector}`));
      throw error;
    }
  }

  async navigateTo(url) {
    try {
      console.log(chalk.blue(`🌐 Navigating to: ${url}`));
      await this.page.goto(url, { waitUntil: 'networkidle' });
      await this.page.waitForTimeout(config.delays.navigation);
    } catch (error) {
      console.error(chalk.red(`❌ Failed to navigate to: ${url}`));
      throw error;
    }
  }

  async takeScreenshot(name) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `screenshot-${name}-${timestamp}.png`;
      await this.page.screenshot({ path: `./screenshots/${filename}` });
      console.log(chalk.green(`📸 Screenshot saved: ${filename}`));
    } catch (error) {
      console.error(chalk.red('❌ Failed to take screenshot:'), error);
    }
  }

  async waitForNavigation() {
    try {
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(config.delays.navigation);
    } catch (error) {
      console.error(chalk.red('❌ Navigation timeout'));
      throw error;
    }
  }

  async handleDialog(accept = true) {
    this.page.on('dialog', async dialog => {
      console.log(chalk.yellow(`⚠️ Dialog: ${dialog.message()}`));
      if (accept) {
        await dialog.accept();
      } else {
        await dialog.dismiss();
      }
    });
  }

  async scrollToBottom() {
    await this.page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await this.page.waitForTimeout(1000);
  }

  async isElementVisible(selector) {
    try {
      const element = await this.page.$(selector);
      return element ? await element.isVisible() : false;
    } catch {
      return false;
    }
  }
}

export default BrowserManager;
