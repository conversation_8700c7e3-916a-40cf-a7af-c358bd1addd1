import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

export const config = {
  // Browser settings
  browser: {
    headless: process.env.HEADLESS === 'true',
    timeout: parseInt(process.env.BROWSER_TIMEOUT) || 30000,
    slowMo: parseInt(process.env.SLOW_MO) || 100,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  },

  // YouTube settings
  youtube: {
    email: process.env.YOUTUBE_EMAIL,
    password: process.env.YOUTUBE_PASSWORD,
    channelName: process.env.YOUTUBE_CHANNEL_NAME,
    baseUrl: 'https://www.youtube.com',
    studioUrl: 'https://studio.youtube.com',
    trustedChannels: process.env.TRUSTED_CHANNELS?.split(',') || []
  },

  // Instagram settings
  instagram: {
    username: process.env.INSTAGRAM_USERNAME,
    password: process.env.INSTAGRAM_PASSWORD,
    baseUrl: 'https://www.instagram.com'
  },

  // Video processing settings
  video: {
    maxDuration: parseInt(process.env.MAX_VIDEO_DURATION) || 60,
    outputResolution: process.env.OUTPUT_RESOLUTION || '1080x1920',
    quality: process.env.VIDEO_QUALITY || 'high',
    formats: {
      youtube: {
        width: 1080,
        height: 1920,
        fps: 30,
        codec: 'libopenh264'
      },
      instagram: {
        width: 1080,
        height: 1920,
        fps: 30,
        codec: 'libopenh264'
      }
    }
  },

  // Download settings
  downloads: {
    maxPerRun: parseInt(process.env.MAX_DOWNLOADS_PER_RUN) || 5,
    quality: process.env.DOWNLOAD_QUALITY || 'best',
    outputDir: {
      youtube: './downloads/youtube',
      instagram: './downloads/instagram'
    }
  },

  // Rate limiting
  delays: {
    action: parseInt(process.env.ACTION_DELAY) || 2000,
    upload: parseInt(process.env.UPLOAD_DELAY) || 5000,
    navigation: 3000,
    typing: 100
  },

  // Paths
  paths: {
    downloads: './downloads',
    output: './output',
    temp: './temp'
  }
};

export default config;
