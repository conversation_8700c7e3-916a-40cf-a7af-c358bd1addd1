#!/usr/bin/env node

import chalk from 'chalk';
import config from './src/config/config.js';
import BrowserManager from './src/utils/browser.js';

async function demoBasicFunctionality() {
  console.log(chalk.blue('🎬 Demo: Basic Functionality Test'));
  console.log(chalk.blue('====================================='));
  
  // Test 1: Configuration
  console.log(chalk.yellow('\n1. Testing Configuration...'));
  console.log(chalk.white(`   Browser timeout: ${config.browser.timeout}ms`));
  console.log(chalk.white(`   Video max duration: ${config.video.maxDuration}s`));
  console.log(chalk.white(`   Output resolution: ${config.video.outputResolution}`));
  console.log(chalk.green('   ✅ Configuration loaded successfully'));
  
  // Test 2: Browser automation
  console.log(chalk.yellow('\n2. Testing Browser Automation...'));
  const browser = new BrowserManager();
  
  try {
    await browser.launch();
    console.log(chalk.green('   ✅ Browser launched'));
    
    await browser.navigateTo('https://www.example.com');
    console.log(chalk.green('   ✅ Navigation successful'));
    
    const title = await browser.page.title();
    console.log(chalk.white(`   Page title: "${title}"`));
    
    await browser.close();
    console.log(chalk.green('   ✅ Browser closed'));
  } catch (error) {
    console.error(chalk.red('   ❌ Browser test failed:'), error.message);
  }
  
  // Test 3: Directory structure
  console.log(chalk.yellow('\n3. Testing Directory Structure...'));
  const dirs = ['./downloads/youtube', './downloads/instagram', './output', './temp'];
  
  for (const dir of dirs) {
    try {
      const fs = await import('fs-extra');
      await fs.ensureDir(dir);
      console.log(chalk.green(`   ✅ ${dir} ready`));
    } catch (error) {
      console.error(chalk.red(`   ❌ ${dir} failed:`, error.message));
    }
  }
  
  console.log(chalk.blue('\n====================================='));
  console.log(chalk.green('🎉 Demo completed!'));
  console.log(chalk.yellow('\n📝 Next steps:'));
  console.log(chalk.white('1. Edit .env with your credentials'));
  console.log(chalk.white('2. Run: npm start'));
}

async function demoYouTubeInfo() {
  console.log(chalk.blue('\n🎬 Demo: YouTube Video Info (No Download)'));
  console.log(chalk.blue('=========================================='));
  
  try {
    const YouTubeDownloader = (await import('./src/youtube/downloader.js')).default;
    const downloader = new YouTubeDownloader();
    await downloader.initialize();
    
    // Test getting video info without downloading
    const testVideoUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'; // Rick Roll as test
    console.log(chalk.yellow(`Getting info for: ${testVideoUrl}`));
    
    const info = await downloader.getVideoInfo(testVideoUrl);
    
    if (info) {
      console.log(chalk.green('✅ Video info retrieved:'));
      console.log(chalk.white(`   Title: ${info.title}`));
      console.log(chalk.white(`   Duration: ${info.duration}`));
      console.log(chalk.white(`   Uploader: ${info.uploader}`));
    } else {
      console.log(chalk.yellow('⚠️ Could not retrieve video info'));
    }
  } catch (error) {
    console.error(chalk.red('❌ YouTube info test failed:'), error.message);
  }
}

// Main demo function
async function runDemo() {
  const args = process.argv.slice(2);
  const demoType = args[0] || 'basic';
  
  try {
    switch (demoType) {
      case 'basic':
        await demoBasicFunctionality();
        break;
      case 'youtube':
        await demoYouTubeInfo();
        break;
      case 'all':
        await demoBasicFunctionality();
        await demoYouTubeInfo();
        break;
      default:
        console.log(chalk.yellow('Available demos:'));
        console.log(chalk.white('  node demo.js basic   - Test basic functionality'));
        console.log(chalk.white('  node demo.js youtube - Test YouTube info retrieval'));
        console.log(chalk.white('  node demo.js all     - Run all demos'));
        break;
    }
  } catch (error) {
    console.error(chalk.red('💥 Demo failed:'), error);
    process.exit(1);
  }
}

// Run demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDemo();
}

export default { demoBasicFunctionality, demoYouTubeInfo };
