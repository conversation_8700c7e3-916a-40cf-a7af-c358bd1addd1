#!/usr/bin/env node

import chalk from 'chalk';
import fs from 'fs-extra';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

class SystemOptimizer {
  constructor() {
    this.outputDir = './output';
    this.downloadsDir = './downloads';
    this.tempDir = './temp';
  }

  async analyzeSystem() {
    console.log(chalk.blue('🔍 Analyzing System Performance...'));
    console.log(chalk.blue('====================================='));

    const analysis = {
      files: await this.analyzeFiles(),
      system: await this.analyzeSystemResources(),
      duplicates: await this.findDuplicates(),
      recommendations: []
    };

    this.generateRecommendations(analysis);
    this.printAnalysis(analysis);

    return analysis;
  }

  async analyzeFiles() {
    const analysis = {
      outputFiles: 0,
      downloadFiles: 0,
      tempFiles: 0,
      totalSize: 0,
      oldFiles: 0
    };

    try {
      // Analyze output directory
      if (await fs.pathExists(this.outputDir)) {
        const outputFiles = await fs.readdir(this.outputDir);
        analysis.outputFiles = outputFiles.length;

        for (const file of outputFiles) {
          const filePath = path.join(this.outputDir, file);
          const stats = await fs.stat(filePath);
          analysis.totalSize += stats.size;

          // Check for old files (older than 7 days)
          const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
          if (stats.mtime.getTime() < sevenDaysAgo) {
            analysis.oldFiles++;
          }
        }
      }

      // Analyze downloads directory
      if (await fs.pathExists(this.downloadsDir)) {
        const downloadFiles = await this.getFilesRecursively(this.downloadsDir);
        analysis.downloadFiles = downloadFiles.length;
      }

      // Analyze temp directory
      if (await fs.pathExists(this.tempDir)) {
        const tempFiles = await fs.readdir(this.tempDir);
        analysis.tempFiles = tempFiles.length;
      }
    } catch (error) {
      console.error(chalk.red('❌ Error analyzing files:'), error);
    }

    return analysis;
  }

  async analyzeSystemResources() {
    const analysis = {
      cpuCores: 0,
      totalMemory: 0,
      freeMemory: 0,
      diskSpace: 0,
      ffmpegVersion: 'Unknown'
    };

    try {
      // Get CPU info
      const cpuInfo = await execAsync('nproc').catch(() => ({ stdout: '1' }));
      analysis.cpuCores = parseInt(cpuInfo.stdout.trim());

      // Get memory info (Linux)
      try {
        const memInfo = await execAsync('free -m');
        const memLines = memInfo.stdout.split('\n');
        const memLine = memLines[1].split(/\s+/);
        analysis.totalMemory = parseInt(memLine[1]);
        analysis.freeMemory = parseInt(memLine[3]);
      } catch (error) {
        // Fallback for other systems
        analysis.totalMemory = 'Unknown';
        analysis.freeMemory = 'Unknown';
      }

      // Get disk space
      try {
        const diskInfo = await execAsync('df -h .');
        const diskLines = diskInfo.stdout.split('\n');
        const diskLine = diskLines[1].split(/\s+/);
        analysis.diskSpace = diskLine[3];
      } catch (error) {
        analysis.diskSpace = 'Unknown';
      }

      // Get FFmpeg version
      try {
        const ffmpegInfo = await execAsync('ffmpeg -version');
        const versionMatch = ffmpegInfo.stdout.match(/ffmpeg version ([^\s]+)/);
        analysis.ffmpegVersion = versionMatch ? versionMatch[1] : 'Unknown';
      } catch (error) {
        analysis.ffmpegVersion = 'Not installed';
      }
    } catch (error) {
      console.error(chalk.red('❌ Error analyzing system:'), error);
    }

    return analysis;
  }

  async findDuplicates() {
    const duplicates = {
      count: 0,
      totalSize: 0,
      files: []
    };

    try {
      if (await fs.pathExists(this.outputDir)) {
        const files = await fs.readdir(this.outputDir);
        const fileGroups = {};

        for (const file of files) {
          if (!file.endsWith('.mp4')) continue;

          // Extract video ID and start time from filename
          const match = file.match(/^(.+?)-(\d+)s-/);
          if (match) {
            const [, videoId, startTime] = match;
            const key = `${videoId}-${startTime}s`;

            if (!fileGroups[key]) {
              fileGroups[key] = [];
            }
            fileGroups[key].push(file);
          }
        }

        // Find duplicates
        for (const [key, group] of Object.entries(fileGroups)) {
          if (group.length > 1) {
            duplicates.count += group.length - 1;
            duplicates.files.push({ key, files: group });

            // Calculate size of duplicates
            for (let i = 1; i < group.length; i++) {
              const filePath = path.join(this.outputDir, group[i]);
              const stats = await fs.stat(filePath);
              duplicates.totalSize += stats.size;
            }
          }
        }
      }
    } catch (error) {
      console.error(chalk.red('❌ Error finding duplicates:'), error);
    }

    return duplicates;
  }

  generateRecommendations(analysis) {
    const recommendations = [];

    // File management recommendations
    if (analysis.files.oldFiles > 10) {
      recommendations.push({
        type: 'cleanup',
        priority: 'high',
        message: `Clean up ${analysis.files.oldFiles} old files to free space`
      });
    }

    if (analysis.files.tempFiles > 0) {
      recommendations.push({
        type: 'cleanup',
        priority: 'medium',
        message: `Clean up ${analysis.files.tempFiles} temporary files`
      });
    }

    // Duplicate recommendations
    if (analysis.duplicates.count > 0) {
      const sizeMB = (analysis.duplicates.totalSize / (1024 * 1024)).toFixed(2);
      recommendations.push({
        type: 'duplicates',
        priority: 'high',
        message: `Remove ${analysis.duplicates.count} duplicate files (${sizeMB} MB)`
      });
    }

    // Performance recommendations
    if (analysis.system.cpuCores >= 4) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: 'Consider increasing parallel processing (you have enough CPU cores)'
      });
    }

    if (analysis.system.freeMemory !== 'Unknown' && analysis.system.freeMemory < 1000) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'Low memory detected - consider reducing batch sizes'
      });
    }

    analysis.recommendations = recommendations;
  }

  async getFilesRecursively(dir) {
    const files = [];
    const items = await fs.readdir(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = await fs.stat(fullPath);

      if (stats.isDirectory()) {
        const subFiles = await this.getFilesRecursively(fullPath);
        files.push(...subFiles);
      } else {
        files.push(fullPath);
      }
    }

    return files;
  }

  printAnalysis(analysis) {
    console.log(chalk.blue('\n📊 System Analysis Results'));
    console.log(chalk.blue('=========================='));

    // File statistics
    console.log(chalk.yellow('\n📁 File Statistics:'));
    console.log(chalk.white(`   Output files: ${analysis.files.outputFiles}`));
    console.log(chalk.white(`   Download files: ${analysis.files.downloadFiles}`));
    console.log(chalk.white(`   Temp files: ${analysis.files.tempFiles}`));
    console.log(chalk.white(`   Total size: ${(analysis.files.totalSize / (1024 * 1024)).toFixed(2)} MB`));
    console.log(chalk.white(`   Old files: ${analysis.files.oldFiles}`));

    // System resources
    console.log(chalk.yellow('\n💻 System Resources:'));
    console.log(chalk.white(`   CPU cores: ${analysis.system.cpuCores}`));
    console.log(chalk.white(`   Total memory: ${analysis.system.totalMemory} MB`));
    console.log(chalk.white(`   Free memory: ${analysis.system.freeMemory} MB`));
    console.log(chalk.white(`   Free disk space: ${analysis.system.diskSpace}`));
    console.log(chalk.white(`   FFmpeg version: ${analysis.system.ffmpegVersion}`));

    // Duplicates
    console.log(chalk.yellow('\n🔄 Duplicates:'));
    console.log(chalk.white(`   Duplicate files: ${analysis.duplicates.count}`));
    console.log(chalk.white(`   Wasted space: ${(analysis.duplicates.totalSize / (1024 * 1024)).toFixed(2)} MB`));

    // Recommendations
    console.log(chalk.yellow('\n💡 Recommendations:'));
    if (analysis.recommendations.length === 0) {
      console.log(chalk.green('   ✅ System is optimized!'));
    } else {
      for (const rec of analysis.recommendations) {
        const priority = rec.priority === 'high' ? chalk.red('HIGH') : 
                        rec.priority === 'medium' ? chalk.yellow('MEDIUM') : chalk.blue('LOW');
        console.log(chalk.white(`   ${priority}: ${rec.message}`));
      }
    }

    console.log(chalk.blue('\n==========================\n'));
  }

  async optimize() {
    console.log(chalk.blue('🚀 Starting System Optimization...'));
    
    const analysis = await this.analyzeSystem();
    
    // Auto-apply safe optimizations
    let optimized = 0;

    // Clean temp files
    if (analysis.files.tempFiles > 0) {
      try {
        await fs.emptyDir(this.tempDir);
        console.log(chalk.green(`✅ Cleaned ${analysis.files.tempFiles} temp files`));
        optimized++;
      } catch (error) {
        console.error(chalk.red('❌ Failed to clean temp files:'), error);
      }
    }

    // Remove duplicates
    if (analysis.duplicates.count > 0) {
      try {
        let removed = 0;
        for (const group of analysis.duplicates.files) {
          // Sort by timestamp and keep the newest
          group.files.sort((a, b) => {
            const timestampA = a.match(/-(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)\.mp4$/)?.[1];
            const timestampB = b.match(/-(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)\.mp4$/)?.[1];
            return timestampB?.localeCompare(timestampA) || 0;
          });

          // Remove all but the newest
          for (let i = 1; i < group.files.length; i++) {
            const filePath = path.join(this.outputDir, group.files[i]);
            await fs.remove(filePath);
            removed++;
          }
        }
        console.log(chalk.green(`✅ Removed ${removed} duplicate files`));
        optimized++;
      } catch (error) {
        console.error(chalk.red('❌ Failed to remove duplicates:'), error);
      }
    }

    if (optimized === 0) {
      console.log(chalk.green('✅ System is already optimized!'));
    } else {
      console.log(chalk.green(`🎉 Optimization complete! Applied ${optimized} optimizations.`));
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'analyze';

  const optimizer = new SystemOptimizer();

  try {
    switch (command) {
      case 'analyze':
        await optimizer.analyzeSystem();
        break;
      case 'optimize':
        await optimizer.optimize();
        break;
      default:
        console.log(chalk.yellow('Available commands:'));
        console.log(chalk.white('  node optimize.js analyze  - Analyze system performance'));
        console.log(chalk.white('  node optimize.js optimize - Analyze and optimize system'));
        break;
    }
  } catch (error) {
    console.error(chalk.red('💥 Optimization failed:'), error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default SystemOptimizer;
