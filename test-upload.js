#!/usr/bin/env node

import YouTubeUploader from './src/youtube/uploader.js';
import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';

async function testUpload() {
  try {
    console.log(chalk.blue('🧪 Testing YouTube upload functionality...'));
    
    // Find a test video file
    const outputDir = './output';
    const files = await fs.readdir(outputDir);
    const testFile = files.find(file => file.includes('captioned') && file.endsWith('.mp4'));
    
    if (!testFile) {
      console.log(chalk.red('❌ No test video found'));
      return;
    }
    
    const testVideoPath = path.join(outputDir, testFile);
    console.log(chalk.blue(`📹 Test video: ${testFile}`));
    
    // Initialize uploader
    const uploader = new YouTubeUploader();
    await uploader.initialize();
    
    // Test metadata
    const metadata = {
      title: 'Test Upload - Shorts',
      description: 'Test upload from automation system\n\n#Shorts #Test',
      tags: ['Shorts', 'Test', 'Automation'],
      visibility: 'private' // Use private for testing
    };
    
    console.log(chalk.blue('🚀 Starting upload test...'));
    const result = await uploader.uploadShorts(testVideoPath, metadata);
    
    console.log(chalk.green('✅ Upload test result:'), result);
    
    await uploader.close();
    
  } catch (error) {
    console.error(chalk.red('❌ Upload test failed:'), error);
  }
}

testUpload();
