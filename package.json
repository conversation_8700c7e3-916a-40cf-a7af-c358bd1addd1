{"name": "automation_videos", "version": "1.0.0", "main": "main.js", "type": "module", "scripts": {"start": "node main.js", "youtube": "node main.js youtube", "instagram": "node main.js instagram", "test": "node test.js", "setup-test": "node test.js", "demo": "node demo.js", "demo-youtube": "node demo.js youtube", "analyze": "node optimize.js analyze", "optimize": "node optimize.js optimize", "clean": "node optimize.js optimize"}, "keywords": ["automation", "youtube", "instagram", "video", "playwright"], "author": "", "license": "ISC", "description": "Automated video content creation and posting system", "dependencies": {"@playwright/test": "^1.52.0", "chalk": "^5.4.1", "dotenv": "^16.5.0", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "playwright": "^1.52.0"}}