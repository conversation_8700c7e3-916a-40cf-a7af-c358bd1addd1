# Video Automation System

An automated system for downloading, processing, and uploading video content across YouTube and Instagram platforms using Playwright and Node.js.

## Features

- **YouTube Integration**
  - Download videos from trusted channels
  - Convert videos to vertical shorts format
  - Upload shorts to your YouTube channel (manual login)
  
- **Instagram Integration**
  - Download reels from Instagram
  - Process and repost to your Instagram account (manual login)
  
- **Video Processing**
  - Convert horizontal videos to vertical format (1080x1920)
  - Add captions and watermarks
  - Extract highlights from longer videos
  - Optimize for mobile viewing

## Prerequisites

- Node.js 18+ 
- FFmpeg installed on your system
- Chrome/Chromium browser
- YouTube and Instagram accounts

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

4. Install FFmpeg (if not already installed):
   - **Ubuntu/Debian**: `sudo apt install ffmpeg`
   - **macOS**: `brew install ffmpeg`
   - **Windows**: Download from https://ffmpeg.org/

## Configuration

1. Copy the environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your credentials:
   ```env
   # YouTube Configuration
   YOUTUBE_EMAIL=<EMAIL>
   YOUTUBE_PASSWORD=your-password
   YOUTUBE_CHANNEL_NAME=Your Channel Name

   # Instagram Configuration
   INSTAGRAM_USERNAME=your-username
   INSTAGRAM_PASSWORD=your-password

   # Trusted YouTube Channels (comma separated)
   TRUSTED_CHANNELS=@channel1,@channel2,@channel3
   ```

## Usage

### Run Full Automation
```bash
npm start
# or
node main.js full
```

### YouTube Only
```bash
npm run youtube
# or
node main.js youtube
```

### Instagram Only
```bash
npm run instagram
# or
node main.js instagram
```

## How It Works

### YouTube Workflow
1. Downloads videos from your trusted channels list
2. Converts them to vertical shorts format (1080x1920)
3. Adds #Shorts hashtag and metadata
4. Uploads to your YouTube channel via browser automation

### Instagram Workflow
1. Downloads reels from Instagram hashtags or profiles
2. Processes them for optimal Instagram format
3. Uploads to your Instagram account via browser automation

## Project Structure

```
├── src/
│   ├── config/
│   │   └── config.js          # Configuration management
│   ├── youtube/
│   │   ├── downloader.js      # YouTube video downloading
│   │   └── uploader.js        # YouTube upload automation
│   ├── instagram/
│   │   ├── downloader.js      # Instagram reels downloading
│   │   └── uploader.js        # Instagram upload automation
│   ├── video/
│   │   └── processor.js       # Video processing and editing
│   └── utils/
│       └── browser.js         # Browser automation utilities
├── downloads/                 # Downloaded content
├── output/                    # Processed videos
├── main.js                    # Main orchestration script
└── README.md
```

## Important Notes

### Manual Login Required
- This system uses browser automation without API keys
- You'll need to manually handle 2FA if enabled
- The browser will open in non-headless mode for login

### Rate Limiting
- Built-in delays to avoid detection
- Configurable timing in `.env` file
- Respects platform rate limits

### Content Guidelines
- Only download from channels you have permission to use
- Ensure compliance with platform terms of service
- Add proper attribution when required

### Security
- Store credentials securely
- Use app-specific passwords when possible
- Consider using environment variables for production

## Troubleshooting

### Browser Issues
- Ensure Playwright browsers are installed: `npx playwright install`
- Check system dependencies for your OS
- Try running in non-headless mode: `HEADLESS=false`

### Video Processing Issues
- Verify FFmpeg is installed and in PATH
- Check video file formats are supported
- Ensure sufficient disk space

### Login Issues
- Disable 2FA temporarily for testing
- Use app-specific passwords
- Check for CAPTCHA requirements

## Customization

### Adding New Channels
Edit the `TRUSTED_CHANNELS` in your `.env` file:
```env
TRUSTED_CHANNELS=@channel1,@channel2,@newchannel
```

### Video Processing Options
Modify `src/config/config.js` for:
- Output resolution
- Video quality settings
- Processing parameters

### Upload Metadata
Customize titles, descriptions, and tags in the workflow functions.

## Legal Disclaimer

This tool is for educational purposes. Users are responsible for:
- Complying with platform terms of service
- Respecting copyright and intellectual property rights
- Obtaining necessary permissions for content use
- Following applicable laws and regulations

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review console output for error details
3. Ensure all dependencies are properly installed
4. Verify configuration settings

## License

This project is provided as-is for educational purposes.
